"use client";

import React, { useState, useEffect, useRef, cache, useMemo, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faFlag, faTimes, faPlay, faSave, faSyncAlt, faChevronLeft, faChevronRight,
  faThList, faCheckCircle, faUsers, faCalendarAlt
} from '@fortawesome/free-solid-svg-icons';
import ExamService, {Option} from "@/app/service/exam-service";
import type {ExamDetail, ExamProblem} from '@/app/service/exam-service'
import CodeEditor from '@/app/components/CodeEditor';
import JudgeService, {TestJudgeResult} from "@/app/service/judge-service";
import {JUDGE_STATUS} from "@/app/utils/constant";
import CommonDropdown, { DropdownOption } from '@/app/components/CommonDropdown';
import MarkdownRenderer from "@/app/components/MarkdownRenderer";
import { EXAM_DIFFICULTY } from '@/app/utils/constant';

// localStorage键名常量
const LANGUAGE_STORAGE_KEY = 'preferred_code_language';

// 为JUDGE_STATUS添加索引签名以解决类型问题
type JudgeStatusType = typeof JUDGE_STATUS & {
  [key: string]: {
    name: string;
    short?: string;
    color: string;
    type: string;
    rgb: string;
  }
};

interface Section {
  title: string;
  description?: string;
  totalScore: number;
  questions: ExamProblem[];
}

// 为编程题的Markdown内容创建一个单独的组件
interface ProgrammingQuestionContentProps {
  description?: string;
  input?: string;
  output?: string;
  examples: string;
  hint?: string;
}

const ProgrammingQuestionContent: React.FC<ProgrammingQuestionContentProps> = React.memo(({
  description = '',
  input = '',
  output = '',
  examples,
  hint = '',
}) => {
  // 示例解析
  const parseExamples = (value: string) => {
    const reg = '<input>([\\s\\S]*?)</input><o>([\\s\\S]*?)</o>';
    const re = RegExp(reg, 'g');
    const objList: {input: string, output: string}[] = [];
    let tmp = re.exec(value);
    while (tmp) {
      objList.push({input: tmp[1], output: tmp[2]});
      tmp = re.exec(value);
    }
    return objList;
  };

  const parsedExamples = parseExamples(examples);

  return (
    <div className="bg-gray-50 p-4 rounded-md mb-4">
      <h4 className="font-medium mb-2">题目描述：</h4>
      <MarkdownRenderer content={description} />

      <h4 className="font-medium mb-2">输入格式：</h4>
      <MarkdownRenderer content={input} />

      <h4 className="font-medium mb-2">输出格式：</h4>
      <MarkdownRenderer content={output} />

      {parsedExamples.map((example, index) => (
        <div key={index} className="mb-4">
          <h4 className="font-medium mb-2">示例 {index + 1}</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <h3 className="font-medium text-gray-700 mb-1">输入:</h3>
              <div className="bg-gray-100 p-3 rounded-md">
                <pre className="text-gray-800 whitespace-pre-wrap text-sm font-mono">{example.input}</pre>
              </div>
            </div>
            <div>
              <h3 className="font-medium text-gray-700 mb-1">输出:</h3>
              <div className="bg-gray-100 p-3 rounded-md">
                <pre className="text-gray-800 whitespace-pre-wrap text-sm font-mono">{example.output}</pre>
              </div>
            </div>
          </div>
        </div>
      ))}

      <h4 className="font-medium mb-2">数据范围：</h4>
      <MarkdownRenderer content={hint} />
    </div>
  );
});

ProgrammingQuestionContent.displayName = 'ProgrammingQuestionContent';

export default function ExamDetail() {
  // Use useParams hook
  const params = useParams<{ id: string }>();
  const [id, setId] = useState<string | null>(null);

  const router = useRouter();

  const [exam, setExam] = useState<ExamDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // 当前题目位置
  const [currentSection, setCurrentSection] = useState(0);
  const [currentQuestion, setCurrentQuestion] = useState(0);

  // 答题状态跟踪
  const [answers, setAnswers] = useState<Record<number, any>>({});
  const [questionStatus, setQuestionStatus] = useState<Record<number, 'completed' | 'marked' | 'current' | 'pending'>>({});
  // 额外添加一个状态来跟踪哪些题目被标记了
  const [markedQuestions, setMarkedQuestions] = useState<Record<number, boolean>>({});

  // 编程题特有状态
  const [selectedLanguage, setSelectedLanguage] = useState<string>('Python3');
  const [code, setCode] = useState<Record<number, Record<string, string>>>({});
  const [output, setOutput] = useState<Record<number, string>>({});
  const [input, setInput] = useState<Record<number, string>>({});

  // 添加防抖定时器
  const debounceTimerRef = useRef<Record<number, NodeJS.Timeout>>({});

  // 保存状态
  const [saveStatus, setSaveStatus] = useState<Record<number, 'saving' | 'saved' | 'normal'>>({});
  const saveTimerRef = useRef<Record<number, NodeJS.Timeout>>({});

  // 是否显示答题卡
  const [showAnswerCard, setShowAnswerCard] = useState(false);

  // 剩余时间
  const [remainingTime, setRemainingTime] = useState<number>(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const dataFetchedRef = useRef(false);
  const [sectionIndex, setSectionIndex] = useState<number>(0)
  const [questionIndex, setQuestionIndex] = useState<number>(0)
  const [sections, setSections] = useState<Section[]>([])
  const [runResult, setRunResult] = useState<Record<number, TestJudgeResult | null>>({})
  const [isRunning, setIsRunning] = useState<Record<number, boolean>>({})

  // 添加新的状态
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  // 初始化组件时从localStorage读取语言偏好
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedLanguage = localStorage.getItem(LANGUAGE_STORAGE_KEY);
      if (savedLanguage) {
        setSelectedLanguage(savedLanguage);
      } else {
        // 默认使用Python3
        setSelectedLanguage('Python3');
      }
    }
  }, []);

  // Extract id from params in useEffect
  useEffect(() => {
    if (params.id && !dataFetchedRef.current) {
      dataFetchedRef.current = true
      setId(params.id);
      fetchExamDetail();
    }
  }, [params]);

  // 在组件卸载时清除所有定时器
  useEffect(() => {
    return () => {
      // 清理所有防抖定时器
      Object.values(debounceTimerRef.current).forEach(timer => {
        clearTimeout(timer);
      });
    };
  }, []);

  const fetchExamDetail = async () => {
    const examId = id || params.id
    console.log(examId)
    ExamService.getExamDetail(examId).then(resp => {
      console.log(resp)
      // 假设ExamService.getExamDetail返回的是包含data属性的响应对象
      const examData = resp.data
      setExam(examData)
      initializeExamState(examData)
    }).finally(() => {
      setLoading(false)
    })
  };

  const initializeExamState = (examData: ExamDetail) => {

    const startTime = examData.startTime
    const remainingTime = Math.ceil((startTime + examData.duration * 60000 - new Date().getTime()) / 1000)
    console.log(remainingTime)

    // 初始化剩余时间
    setRemainingTime(remainingTime);

    // 开始倒计时
    startTimer();

    // 初始化问题状态
    const initialStatus: Record<number, 'completed' | 'marked' | 'current' | 'pending'> = {};

    const sections: Section[] = []
    if (examData.singleChoiceList && examData.singleChoiceList.length > 0) {
      const questions = examData.singleChoiceList
      questions.forEach((question) => {
        const optionsJson = question.options
        const options: Option[] = JSON.parse(optionsJson)
        options.forEach((option: Option, index: number) => {
          option.id = String.fromCharCode(65 + index)
        })
        question.optionArray = options
        question.score = examData.singleChoiceScore
        if (question.value) {
          setAnswers(prev => ({
            ...prev,
            [question.id]: question.value
          }));
        }
      })
      sections.push({
        title: '单选题',
        description: `共${examData.singleChoiceList.length}题，每题${examData.singleChoiceScore}分`,
        totalScore: examData.singleChoiceScore * examData.singleChoiceList.length,
        questions: questions
      })
    }
    if (examData.multipleChoiceList && examData.multipleChoiceList.length > 0) {
      const questions = examData.multipleChoiceList
      questions.forEach((question) => {
        const optionsJson = question.options
        const options: Option[] = JSON.parse(optionsJson)
        options.forEach((option: Option, index: number) => {
          option.id = String.fromCharCode(65 + index)
        })
        question.optionArray = options
        question.score = examData.multipleChoiceScore

        if (question.value) {
          setAnswers(prev => ({
            ...prev,
            [question.id]: question.value.split(',')
          }));
        }
      })
      sections.push({
        title: '多选题',
        description: `共${examData.multipleChoiceList.length}题，每题${examData.multipleChoiceScore}分`,
        totalScore: examData.multipleChoiceScore * examData.multipleChoiceList.length,
        questions: questions
      })
    }
    if (examData.trueOrFalseList && examData.trueOrFalseList.length > 0) {
      const questions = examData.trueOrFalseList
      questions.forEach((question) => {
        question.optionArray = [
          {id: 'A', content: '正确'},
          {id: 'B', content: '错误'}
        ]
        question.score = examData.trueOrFalseScore
        if (question.value) {
          setAnswers(prev => ({
            ...prev,
            [question.id]: question.value
          }));
        }
      })
      sections.push({
        title: '判断题',
        description: `共${examData.trueOrFalseList.length}题，每题${examData.trueOrFalseScore}分`,
        totalScore: examData.trueOrFalseScore * examData.trueOrFalseList.length,
        questions: questions
      })
    }
    if (examData.ojList && examData.ojList.length > 0) {
      const questions = examData.ojList
      questions.forEach((question) => {
        question.score = examData.ojScore
        if (question.value) {
          setCode(prev => ({
            ...prev,
            [question.id]: {
              ...prev[question.id],
              [question.language]: question.value
            }
          }));
        }
      })
      sections.push({
        title: '编程题',
        description: `共${examData.ojList.length}题，每题${examData.ojScore}分`,
        totalScore: examData.ojScore * examData.ojList.length,
        questions: examData.ojList
      })
    }

    sections.forEach((section, sectionIdx) => {
      section.questions.forEach((question, questionIdx) => {
        const qId = question.id;

        if (sectionIdx === sectionIndex &&
          questionIdx === questionIndex) {
          initialStatus[qId] = 'current';
        } else if (question.value) {
          initialStatus[qId] = 'completed';
        } else {
          initialStatus[qId] = 'pending';
        }

        // 如果是编程题，初始化代码
        // if (question.problemType === 'programming' && question.defaultCode) {
        //   setCode(prev => ({
        //     ...prev,
        //     [qId]: question.defaultCode || {}
        //   }));
        // }
      });
    });
    setSections(sections)

    setQuestionStatus(initialStatus);
  };

  const startTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(() => {
      setRemainingTime(prev => {
        if (prev <= 0) {
          if (timerRef.current) {
            clearInterval(timerRef.current);
          }
          // 考试时间到，自动交卷
          handleSubmitExam(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const calculateProgress = () => {
    if (!exam) return 0;

    let completed = 0;
    let total = 0;

    Object.values(questionStatus).forEach(status => {
      total++;
      if (status === 'completed') {
        completed++;
      }
    });

    return total ? Math.round((completed / total) * 100) : 0;
  };

  const handleQuestionClick = (sectionIndex: number, questionIndex: number, questionId: number) => {
    // 更新当前问题状态
    const currentQuestionId = sections[currentSection].questions[currentQuestion].id;
    if (currentQuestionId) {
      setQuestionStatus(prev => ({
        ...prev,
        // 保留标记状态，只有在非标记状态下才根据答题情况设置状态
        [currentQuestionId]: answers[currentQuestionId] ? 'completed' : 'pending'
      }));
    }

    setCurrentSection(sectionIndex);
    setCurrentQuestion(questionIndex);

    // 关闭答题卡
    setShowAnswerCard(false);
  };

  // 获取已存在的代码
  const getExistingCode = useCallback((questionId: number, language: string): string => {
    return code[questionId]?.[language] || '';
  }, [code]);

  // 使用useCallback优化saveAnswer函数，处理保存答案
  const saveAnswer = useCallback((questionId: number, answer?: string, language?: string, showStatusUpdate: boolean = false) => {
    if (!exam?.userTestPaperRecordId) return Promise.resolve(false);

    // 如果需要显示状态更新，设置为保存中
    if (showStatusUpdate) {
      setSaveStatus(prev => ({
        ...prev,
        [questionId]: 'saving'
      }));
    }

    const data = {
      userTestPaperRecordId: exam.userTestPaperRecordId,
      problemSubmitView: {
        testPaperProblemId: questionId,
        value: answer,
        language: language
      }
    }

    // 发送保存请求
    return ExamService.saveAnswer(data).then(() => {
      console.log('auto save complete', data);

      // 如果需要显示状态更新，设置为已保存
      if (showStatusUpdate) {
        setSaveStatus(prev => ({
          ...prev,
          [questionId]: 'saved'
        }));

        // 3秒后恢复正常状态
        if (saveTimerRef.current[questionId]) {
          clearTimeout(saveTimerRef.current[questionId]);
        }

        saveTimerRef.current[questionId] = setTimeout(() => {
          setSaveStatus(prev => ({
            ...prev,
            [questionId]: 'normal'
          }));
        }, 3000);
      }

      return true;
    }).catch(error => {
      console.error('保存失败', error);

      // 如果需要显示状态更新，恢复正常状态
      if (showStatusUpdate) {
        setSaveStatus(prev => ({
          ...prev,
          [questionId]: 'normal'
        }));
      }

      return false;
    });
  }, [exam, saveTimerRef]);

  // 处理防抖保存，用于优化用户输入时的保存频率
  const debounceSaveAnswer = useCallback((questionId: number, answer?: string, language?: string, showStatusUpdate: boolean = false) => {
    // 如果已经有一个计时器在运行，先清除它
    if (debounceTimerRef.current[questionId]) {
      clearTimeout(debounceTimerRef.current[questionId]);
    }

    // 设置一个新的计时器，500ms后才执行保存
    debounceTimerRef.current[questionId] = setTimeout(() => {
      saveAnswer(questionId, answer, language, showStatusUpdate);
    }, 500);
  }, [saveAnswer]);

  // 使用useCallback优化handleAnswerChange，处理答案变更
  const handleAnswerChange = useCallback((questionId?: number, answer?: any, language?: string) => {
    if (questionId === undefined) return;

    // 更新本地状态
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));

    // 处理数组类型答案
    let processedAnswer = answer;
    if (typeof processedAnswer !== 'string' && Array.isArray(processedAnswer)) {
      processedAnswer = processedAnswer.sort().join(',')
    }

    // 使用防抖来调用保存答案，减少请求频率
    debounceSaveAnswer(questionId, processedAnswer, language);
  }, [debounceSaveAnswer]);

  // 使用useCallback优化handleCodeChange，处理代码变更
  const handleCodeChange = useCallback((questionId?: number, value?: string) => {
    if (questionId === undefined || !value) return;

    // 检查代码是否真的发生了变化，避免不必要的保存
    const prevCode = code[questionId]?.[selectedLanguage] || '';
    if (prevCode === value) {
      return; // 如果代码没有变化，不执行任何操作
    }

    // 代码发生变化，更新状态
    setCode(prev => ({
      ...prev,
      [questionId]: {
        ...prev[questionId],
        [selectedLanguage]: value
      }
    }));

    // 使用防抖保存代码更改
    debounceSaveAnswer(questionId, value, selectedLanguage, true);
  }, [code, selectedLanguage, debounceSaveAnswer]);

  // 用户点击保存按钮时调用，直接保存不使用防抖
  const handleSaveCode = useCallback((questionId?: number, code?: string, language?: string) => {
    if (questionId === undefined || !code || !language) return;

    // 检查代码是否有变化
    const prevCode = getExistingCode(questionId, language);

    if (prevCode !== code) {
      // 用户手动保存时直接调用保存，并显示状态
      saveAnswer(questionId, code, language, true);
    } else {
      // 如果代码没有变化，显示已保存状态一会儿
      setSaveStatus(prev => ({
        ...prev,
        [questionId]: 'saved'
      }));

      // 3秒后恢复正常状态
      if (saveTimerRef.current[questionId]) {
        clearTimeout(saveTimerRef.current[questionId]);
      }

      saveTimerRef.current[questionId] = setTimeout(() => {
        setSaveStatus(prev => ({
          ...prev,
          [questionId]: 'normal'
        }));
      }, 3000);
    }
  }, [getExistingCode, saveAnswer, saveTimerRef]);

  // 获取运行结果
  const getTestResult = (key: string, problemId: number) => {
    // 设置一个最大重试次数，防止无限轮询
    let retryCount = 0;
    const maxRetries = 30; // 最多等待30秒

    const fetchResult = () => {
      const t = setTimeout(async () => {
        try {
          retryCount++;
          const resp = await JudgeService.getTestResult(key);

          if (resp.data?.status !== 5) {
            // 获取到最终结果
            setRunResult((prev) => ({
              ...prev,
              [problemId]: resp.data
            }));
            setIsRunning(prev => ({
              ...prev,
              [problemId]: false
            }));
            clearTimeout(t);
          } else if (retryCount >= maxRetries) {
            // 超过最大重试次数，认为超时
            setIsRunning(prev => ({
              ...prev,
              [problemId]: false
            }));
            setRunResult((prev) => ({
              ...prev,
              [problemId]: {
                ...resp.data,
                status: -1,
                stderr: "运行超时，请检查您的代码是否有死循环"
              }
            }));
            clearTimeout(t);
          } else {
            // 继续轮询
            fetchResult();
          }
        } catch (error) {
          console.error("获取运行结果失败", error);
          setIsRunning(prev => ({
            ...prev,
            [problemId]: false
          }));
          clearTimeout(t);
        }
      }, 1000);
    };

    fetchResult();
  }

  const handleRunCode = (problem: ExamProblem | null) => {
    if (!problem) return;

    const currentCode = code[problem.id]?.[selectedLanguage] || '';
    const currentQuestion = getCurrentQuestion();

    if (!currentQuestion) return;

    // 设置运行状态为true
    setIsRunning(prev => ({
      ...prev,
      [problem.id]: true
    }));

    const data = {
      pid: problem.problemId,
      code: currentCode,
      language: selectedLanguage,
      userInput: input[problem.id] || '',
      expectedOutput: '',
      type: 'public',
      isRemoteJudge: false,
      cid: 0
    }
    console.log(data)

    JudgeService.submitTest(data).then(resp => {
      console.log(resp)
      getTestResult(resp.data, problem.id)
    }).catch(error => {
      console.error('运行失败', error);
      // 设置运行状态为false
      setIsRunning(prev => ({
        ...prev,
        [problem.id]: false
      }));
    })

    // 标记为已完成
    setQuestionStatus(prev => {
      // 如果是标记的状态，保持标记
      if (markedQuestions[problem.id]) {
        return { ...prev, [problem.id]: 'marked' };
      }
      return { ...prev, [problem.id]: 'completed' };
    });
  };

  const handleToggleMark = (questionId?: number) => {
    if (questionId === undefined) return;

    // 更新标记状态
    setMarkedQuestions(prev => ({
      ...prev,
      [questionId]: !prev[questionId]
    }));
  };

  const handlePrevQuestion = () => {
    if (!sections.length) return;

    let newSectionIndex = currentSection;
    let newQuestionIndex = currentQuestion - 1;

    if (newQuestionIndex < 0) {
      newSectionIndex = currentSection - 1;
      if (newSectionIndex < 0) {
        // 已经是第一题
        return;
      }
      newQuestionIndex = sections[newSectionIndex].questions.length - 1;
    }

    const questionId = sections[newSectionIndex].questions[newQuestionIndex].id;
    handleQuestionClick(newSectionIndex, newQuestionIndex, questionId);
  };

  const handleNextQuestion = () => {
    if (!sections.length) return;

    let newSectionIndex = currentSection;
    let newQuestionIndex = currentQuestion + 1;

    if (newQuestionIndex >= sections[newSectionIndex].questions.length) {
      newSectionIndex = currentSection + 1;
      if (newSectionIndex >= sections.length) {
        // 已经是最后一题
        return;
      }
      newQuestionIndex = 0;
    }

    const questionId = sections[newSectionIndex].questions[newQuestionIndex].id;
    handleQuestionClick(newSectionIndex, newQuestionIndex, questionId);
  };

  const handleSubmitExam = (timeout: boolean = false) => {
    if (!timeout) {
      // 显示确认弹框而不是使用window.confirm
      setShowConfirmModal(true);
    } else {
      // 如果是超时自动交卷，直接执行提交逻辑
      // 这里应该有保存答案和提交考试的逻辑
      doSubmitExam()
    }
  };

  const doSubmitExam = () => {
    // 确认交卷
    ExamService.submit(exam?.userTestPaperRecordId).then(() => {
      router.push('/exams');
    })
  }

  const confirmSubmit = () => {
    // 确认交卷后的逻辑
    // 这里应该有保存答案和提交考试的逻辑
    setShowConfirmModal(false);
    doSubmitExam();
  };

  const getCurrentQuestion = (): ExamProblem | null => {
    return sections[currentSection]?.questions[currentQuestion] || null;
  };

  const getStatusClass = (id: number) => {
    if (currentQuestionData?.id === id) {
      return 'bg-indigo-600 text-white'
    } else if (markedQuestions[id]) {
      return 'bg-yellow-500 text-white';
    } else if (questionStatus[id] === 'completed') {
      return 'bg-green-500 text-white';
    } else {
      return 'bg-gray-200 text-gray-700';
    }
  };

  // 计算考试总分
  const calculateTotalScore = () => {
    if (!exam) return 0;

    let totalScore = 0;

    // 计算单选题总分
    if (exam.singleChoiceList && exam.singleChoiceList.length > 0) {
      totalScore += exam.singleChoiceList.length * exam.singleChoiceScore;
    }

    // 计算多选题总分
    if (exam.multipleChoiceList && exam.multipleChoiceList.length > 0) {
      totalScore += exam.multipleChoiceList.length * exam.multipleChoiceScore;
    }

    // 计算判断题总分
    if (exam.trueOrFalseList && exam.trueOrFalseList.length > 0) {
      totalScore += exam.trueOrFalseList.length * exam.trueOrFalseScore;
    }

    // 计算编程题总分
    if (exam.ojList && exam.ojList.length > 0) {
      totalScore += exam.ojList.length * exam.ojScore;
    }

    return totalScore;
  };

  // 处理语言选择变更
  const handleLanguageChange = (value: string | null) => {
    const newLanguage = value || 'Python3';
    setSelectedLanguage(newLanguage);

    // 保存到localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem(LANGUAGE_STORAGE_KEY, newLanguage);
    }
  };

  // 语言选项
  const languageOptions: DropdownOption<string>[] = [
    { label: 'Python3', value: 'Python3' },
    { label: 'C++', value: 'C++' }
  ];

  if (!id) {
    return <div className="flex justify-center items-center h-screen">
      <div className="text-xl">正在加载考试信息...</div>
    </div>;
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600"></div>
          <p className="mt-3 text-gray-600">正在加载考试内容...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-md max-w-md">
          <h2 className="text-lg font-semibold mb-2">加载失败</h2>
          <p className="mb-3">{error}</p>
          <div className="flex justify-between">
            <button
              onClick={() => router.push('/exams')}
              className="text-red-700 border border-red-300 px-4 py-2 rounded-md hover:bg-red-100"
            >
              返回考试列表
            </button>
            <button
              onClick={fetchExamDetail}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
            >
              重试
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!exam) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <p className="text-xl text-gray-600">未找到考试信息</p>
          <button
            onClick={() => router.push('/exams')}
            className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
          >
            返回考试列表
          </button>
        </div>
      </div>
    );
  }

  const currentQuestionData = getCurrentQuestion();
  const progress = calculateProgress();
  const totalScore = calculateTotalScore();

  return (
    <>
      {/* 考试顶部信息栏 */}
      <div className="sticky top-[64px] z-15 bg-white shadow-md py-3">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-between items-center">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-900">{exam.title}</h1>
              <span className="ml-3 px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded-full">{EXAM_DIFFICULTY[(exam.difficulty || 1) as keyof typeof EXAM_DIFFICULTY].text}</span>
            </div>

            {/* 计时器 */}
            <div className="flex items-center">
              <div className="text-center mr-6">
                <p className="text-sm text-gray-500">剩余时间</p>
                <p className="text-xl font-bold text-red-600">{formatTime(remainingTime)}</p>
              </div>

              {/* 交卷按钮 */}
              <button
                onClick={() => handleSubmitExam(false)}
                className="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                交卷
              </button>
            </div>
          </div>

          {/* 进度条 */}
          <div className="mt-3">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>已完成题目: {Object.values(questionStatus).filter(status => status === 'completed').length}/{Object.keys(questionStatus).length}</span>
              <span>总分: {totalScore}分</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div className="bg-indigo-600 h-2.5 rounded-full" style={{ width: `${progress}%` }}></div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row">
          {/* 左侧题目导航 */}
          <div className="w-full lg:w-64 mb-6 lg:mb-0 lg:mr-8">
            <div className="bg-white shadow-md rounded-lg p-4 sticky top-[200px]">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">题目导航</h2>

              {sections.map((section, sectionIndex) => (
                <div className="mb-4" key={sectionIndex}>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">
                    {sectionIndex + 1}、{section.title} ({section.description})
                  </h3>
                  <div className="grid grid-cols-5 gap-2">
                    {section.questions.map((question, questionIndex) => (
                      <button
                        key={question.id}
                        onClick={() => handleQuestionClick(sectionIndex, questionIndex, question.id)}
                        className={`w-8 h-8 rounded-full font-medium flex items-center justify-center hover:opacity-90 focus:outline-none ${getStatusClass(question.id)}`}
                      >
                        {questionIndex + 1}
                      </button>
                    ))}
                  </div>
                </div>
              ))}

              {/* 图例 */}
              <div className="mt-6 text-xs text-gray-500">
                <div className="flex items-center mb-1">
                  <span className="w-4 h-4 rounded-full bg-green-500 inline-block mr-2"></span>
                  <span>已完成</span>
                </div>
                <div className="flex items-center mb-1">
                  <span className="w-4 h-4 rounded-full bg-yellow-500 inline-block mr-2"></span>
                  <span>标记待复查</span>
                </div>
                <div className="flex items-center mb-1">
                  <span className="w-4 h-4 rounded-full bg-indigo-600 inline-block mr-2"></span>
                  <span>当前题目</span>
                </div>
                <div className="flex items-center">
                  <span className="w-4 h-4 rounded-full bg-gray-200 inline-block mr-2"></span>
                  <span>未作答</span>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧题目内容 */}
          <div className="flex-1 bg-white shadow-md rounded-lg overflow-hidden">
            {currentQuestionData && (
              <>
                {/* 选择题渲染 */}
                {(currentQuestionData.problemType === 1 || currentQuestionData.problemType === 2 || currentQuestionData.problemType === 3) && (
                  <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between mb-4">
                      <h2 className="text-lg font-semibold text-gray-900">
                        {currentSection + 1}、{sections[currentSection].title} 第{currentQuestion + 1}题（{currentQuestionData.score}分）
                      </h2>
                      <div className="flex space-x-3">
                        <button
                          onClick={() => handleToggleMark(currentQuestionData?.id)}
                          className={`focus:outline-none ${markedQuestions[currentQuestionData.id] ? 'text-yellow-500 hover:text-yellow-600' : 'text-gray-500 hover:text-yellow-500'}`}
                        >
                          <FontAwesomeIcon icon={faFlag} className="mr-1" />
                          {markedQuestions[currentQuestionData.id] ? '已标记' : '标记'}
                        </button>
                      </div>
                    </div>

                    <div className="text-gray-700 mb-6">
                      <p className="mb-4">{currentQuestionData.title}</p>
                      {currentQuestionData.description && <div className="mb-4" dangerouslySetInnerHTML={{__html: currentQuestionData.description}}></div>}

                      <div className="space-y-3">
                        {currentQuestionData.optionArray?.map(option => (
                          <label
                            key={option.id}
                            className={`flex items-start p-3 border rounded-md hover:bg-gray-50 cursor-pointer ${
                              answers[currentQuestionData.id] === option.id || 
                              (Array.isArray(answers[currentQuestionData.id]) && 
                                answers[currentQuestionData.id]?.includes(option.id))
                                ? 'border-indigo-600 bg-indigo-50' 
                                : 'border-gray-200'
                            }`}
                          >
                            {currentQuestionData.problemType === 1 || currentQuestionData.problemType === 3 ? (
                              <input
                                type="radio"
                                name={`q${currentQuestionData.id}`}
                                value={option.id}
                                className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                                checked={answers[currentQuestionData.id] === option.id}
                                onChange={() => {
                                  handleAnswerChange(currentQuestionData?.id, option.id);
                                  setQuestionStatus(prev => ({
                                    ...prev,
                                    [currentQuestionData?.id]: 'completed'
                                  }));
                                }}
                              />
                            ) : (
                              <input
                                type="checkbox"
                                className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                checked={Array.isArray(answers[currentQuestionData.id]) &&
                                        answers[currentQuestionData.id]?.includes(option.id)}
                                onChange={(e) => {
                                  const currentAnswers = Array.isArray(answers[currentQuestionData?.id])
                                    ? [...answers[currentQuestionData?.id]]
                                    : [];

                                  const index = currentAnswers.indexOf(option.id);
                                  if (index >= 0) {
                                    currentAnswers.splice(index, 1);
                                  } else {
                                    currentAnswers.push(option.id);
                                  }

                                  handleAnswerChange(currentQuestionData?.id, currentAnswers);
                                  if (currentAnswers.length > 0) {
                                    setQuestionStatus(prev => ({
                                      ...prev,
                                      [currentQuestionData?.id]: 'completed'
                                    }));
                                  }
                                }}
                              />
                            )}
                            <div className="ml-3 flex">{option.id}. <div dangerouslySetInnerHTML={{__html: option.content || ''}}></div></div>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* 编程题渲染 */}
                {currentQuestionData.problemType === 4 && (
                  <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between mb-4">
                      <h2 className="text-lg font-semibold text-gray-900">
                        {currentSection + 1}、{sections[currentSection].title} 第{currentQuestion + 1}题（{currentQuestionData.score}分）
                      </h2>
                      <div className="flex space-x-3">
                        <button
                          onClick={() => handleToggleMark(currentQuestionData?.id)}
                          className={`focus:outline-none ${questionStatus[currentQuestionData.id] === 'marked' ? 'text-yellow-500 hover:text-yellow-600' : 'text-gray-500 hover:text-yellow-500'}`}
                        >
                          <FontAwesomeIcon icon={faFlag} className="mr-1" />
                          {questionStatus[currentQuestionData.id] === 'marked' ? '已标记' : '标记'}
                        </button>
                      </div>
                    </div>

                    <div className="text-gray-700 mb-6">
                      <h3 className="font-medium text-lg mb-2">{currentQuestionData.title}</h3>

                      <ProgrammingQuestionContent
                        description={currentQuestionData?.description}
                        input={currentQuestionData?.input}
                        output={currentQuestionData?.output}
                        examples={currentQuestionData?.examples || ''}
                        hint={currentQuestionData?.hint}
                      />

                      {/* 代码编辑区 */}
                      <div className="mb-4">
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="font-medium">代码：</h4>
                          <CommonDropdown<string | null>
                            label="编程语言"
                            options={languageOptions}
                            value={selectedLanguage}
                            onChange={handleLanguageChange}
                            placeholder="选择语言"
                            className="w-40 h-8 text-sm"
                            canClear={false}
                          />
                        </div>
                        <div className="border border-gray-300 rounded-md overflow-hidden h-[500px]">
                          <div className="bg-gray-100 px-4 py-2 text-sm flex justify-between items-center">
                            <span>编辑器</span>
                            <div className="space-x-2">
                              <button className="text-gray-600 hover:text-indigo-600">
                                <i className="fas fa-expand"></i>
                              </button>
                              <button className="text-gray-600 hover:text-indigo-600">
                                <i className="fas fa-cog"></i>
                              </button>
                            </div>
                          </div>
                          <div className="h-[440px]">
                            <CodeEditor
                              code={code[currentQuestionData.id]?.[selectedLanguage] || ''}
                              language={selectedLanguage.toLowerCase()}
                              onChange={(value) => handleCodeChange(currentQuestionData?.id, value)}
                              className="h-full"
                              darkMode={false}
                            />
                          </div>
                        </div>
                      </div>

                      {/* 测试用例 */}
                      <div className="grid grid-cols-2 gap-4 mb-6">
                        <div>
                          <h4 className="font-medium mb-2">输入：</h4>
                          <textarea
                            className="w-full p-3 border border-gray-300 rounded-md h-24 text-sm font-mono"
                            onChange={(e) => {
                              setInput(prev => ({
                                ...prev,
                                [currentQuestionData?.id]: e.target.value
                              }));
                            }}
                          ></textarea>
                        </div>
                        <div>
                          <h4 className="font-medium mb-2">输出：</h4>
                          <div className="w-full p-3 border border-gray-300 rounded-md h-24 text-sm font-mono bg-gray-50 overflow-auto">
                            {runResult[currentQuestionData.id]?.userOutput || ''}
                          </div>
                        </div>
                      </div>

                      {/* 按钮组 */}
                      <div className="flex space-x-3">
                        <button
                          onClick={() => handleRunCode(currentQuestionData)}
                          disabled={isRunning[currentQuestionData?.id]}
                          className={`px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                            isRunning[currentQuestionData?.id]
                              ? 'bg-indigo-400 text-white cursor-not-allowed'
                              : 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500'
                          }`}
                        >
                          {isRunning[currentQuestionData?.id] ? (
                            <>
                              <div className="inline-block w-4 h-4 mr-2 border-t-2 border-b-2 border-white rounded-full animate-spin"></div>
                              运行中...
                            </>
                          ) : (
                            <>
                              <FontAwesomeIcon icon={faPlay} className="mr-1" /> 运行代码
                            </>
                          )}
                        </button>
                        <button
                          onClick={() => {
                            handleSaveCode(currentQuestionData?.id, code[currentQuestionData?.id]?.[selectedLanguage] || '', selectedLanguage)
                          }}
                          disabled={saveStatus[currentQuestionData?.id] === 'saved'}
                          className={`px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                            saveStatus[currentQuestionData?.id] === 'saving' 
                              ? 'border-yellow-600 text-yellow-600 bg-yellow-50' 
                              : saveStatus[currentQuestionData?.id] === 'saved'
                                ? 'border-gray-300 text-gray-400 bg-gray-50 cursor-not-allowed'
                                : 'border-green-600 text-green-600 hover:bg-green-50 focus:ring-green-500'
                          }`}>
                          {saveStatus[currentQuestionData?.id] === 'saving' ? (
                            <>
                              <div className="inline-block w-4 h-4 mr-2 border-t-2 border-b-2 border-yellow-600 rounded-full animate-spin"></div>
                              保存中...
                            </>
                          ) : saveStatus[currentQuestionData?.id] === 'saved' ? (
                            <>
                              <FontAwesomeIcon icon={faCheckCircle} className="mr-1" /> 已保存
                            </>
                          ) : (
                            <>
                              <FontAwesomeIcon icon={faSave} className="mr-1" /> 保存
                            </>
                          )}
                        </button>
                        <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                          <FontAwesomeIcon icon={faSyncAlt} className="mr-1" /> 重置
                        </button>
                      </div>
                      {runResult[currentQuestionData.id] && runResult[currentQuestionData.id]?.status !== 0 && (
                        <div className={'bg-red-50 text-red-500 text-sm px-3 py-3 mt-2 rounded-xl'}>
                          <div className={'text-red-800'}>
                            {(JUDGE_STATUS as any)[String(runResult[currentQuestionData.id]?.status || '')]?.name || '未知错误'}
                          </div>
                          {
                            (runResult[currentQuestionData.id]?.stderr &&
                              <div className={'text-sm'}
                                   style={{whiteSpace: 'pre-wrap'}}
                                   dangerouslySetInnerHTML={{__html: runResult[currentQuestionData.id]?.stderr || ''}}></div>)
                          }
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex justify-between p-6">
                  <button
                    onClick={handlePrevQuestion}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <FontAwesomeIcon icon={faChevronLeft} className="mr-1" /> 上一题
                  </button>
                  <button
                    onClick={handleNextQuestion}
                    className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    下一题 <FontAwesomeIcon icon={faChevronRight} className="ml-1" />
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </main>

      {/* 显示答题卡按钮 */}
      <button
        onClick={() => setShowAnswerCard(true)}
        className="fixed bottom-4 right-4 bg-indigo-600 text-white px-4 py-2 rounded-md shadow-lg hover:bg-indigo-700 focus:outline-none"
      >
        <FontAwesomeIcon icon={faThList} className="mr-2" /> 答题卡
      </button>

      {/* 答题卡模态框 */}
      {showAnswerCard && (
        <div className="fixed inset-0 bg-gray-500/75 z-50">
          <div className="flex items-center justify-center min-h-screen p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-screen overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold text-gray-900">答题卡</h2>
                  <button
                    onClick={() => setShowAnswerCard(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FontAwesomeIcon icon={faTimes} className="text-xl" />
                  </button>
                </div>

                {sections.map((section, sectionIndex) => (
                  <div className="mb-6" key={sectionIndex}>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">
                      {sectionIndex + 1}、{section.title}
                    </h3>
                    <div className="grid grid-cols-5 sm:grid-cols-10 gap-3">
                      {section.questions.map((question, questionIndex) => (
                        <button
                          key={question.id}
                          onClick={() => {
                            handleQuestionClick(sectionIndex, questionIndex, question.id);
                            setShowAnswerCard(false);
                          }}
                          className={`w-8 h-8 rounded-full font-medium flex items-center justify-center ${getStatusClass(question.id)}`}
                        >
                          {questionIndex + 1}
                        </button>
                      ))}
                    </div>
                  </div>
                ))}

                {/* 按钮 */}
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => setShowAnswerCard(false)}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    关闭
                  </button>
                  <button
                    onClick={() => handleSubmitExam(false)}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    交卷
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 添加确认交卷的模态框 */}
      {showConfirmModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-20 animate-fadeIn">
          <div className="bg-white rounded-lg shadow-2xl p-6 max-w-md w-full mx-4 animate-scaleIn">
            <div className="text-center mb-4">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 text-red-600 mb-4">
                <FontAwesomeIcon icon={faFlag} className="text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">确认交卷</h3>
              <p className="text-gray-600">交卷后将无法再修改答案，确定要提交吗？</p>
            </div>
            <div className="flex justify-center space-x-3 mt-6">
              <button
                onClick={() => setShowConfirmModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 min-w-[100px]"
              >
                取消
              </button>
              <button
                onClick={confirmSubmit}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 min-w-[100px]"
              >
                确认交卷
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
