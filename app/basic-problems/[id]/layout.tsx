"use client";

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {useParams, usePathname, useRouter, useSearchParams} from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRocket, faBars, faBook } from '@fortawesome/free-solid-svg-icons';
import UserInfo from '../../components/UserInfo';
import NotificationDropdown from '../../components/NotificationDropdown';
import NavDrawer from '../../components/NavDrawer';
import ProblemDrawer from '../../components/ProblemDrawer';
import LearningContextService, {LearningContext} from "@/app/service/learning-context-service";

export default function BasicProblemLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const params = useParams<{ id: string }>();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isProblemDrawerOpen, setIsProblemDrawerOpen] = useState(false);
  const navbarRef = useRef<HTMLElement>(null);
  const router = useRouter();
  const [learningContext, setLearningContext] = useState<LearningContext | null>(null);
  const [currentProblemId, setCurrentProblemId] = useState(params.id);

  const queryLearningContext = () => {
    const data = {
      envId: searchParams.get("envId"),
      envType: searchParams.get("envType"),
      currentQuestion: Number(currentProblemId),
      currentQuestionType: 2
    }
    LearningContextService.query(data).then(resp => {
      setLearningContext(resp.data)
    })
  };

  useEffect(() => {
    queryLearningContext()
    // 添加滚动事件监听器
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
        // 设置占位符高度
        const navbarHeight = navbarRef.current?.offsetHeight || 0;
        const placeholder = document.getElementById('navbar-placeholder');
        if (placeholder) {
          placeholder.style.height = `${navbarHeight}px`;
        }
      } else {
        setIsScrolled(false);
        // 恢复占位符高度
        const placeholder = document.getElementById('navbar-placeholder');
        if (placeholder) {
          placeholder.style.height = '0px';
        }
      }
    };

    // 添加滚动事件监听
    window.addEventListener('scroll', handleScroll);

    // 初始检查滚动位置
    handleScroll();

    // 清理事件监听器
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // 当抽屉打开时禁止背景滚动
  useEffect(() => {
    if (isDrawerOpen || isProblemDrawerOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isDrawerOpen, isProblemDrawerOpen]);

  // 判断链接是否为当前页面
  const isActive = (path: string) => {
    if (path === '/') {
      return pathname === path;
    }
    // 对于子路由，例如/basic-problems/1应该匹配/basic-problems
    return pathname.startsWith(path);
  };

  // 获取链接的样式类
  const getLinkClass = (path: string) => {
    return isActive(path)
      ? "text-indigo-600 font-medium" // 当前页面高亮样式
      : "text-gray-600 hover:text-indigo-600"; // 非当前页面样式
  };

  const toggleDrawer = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  const toggleProblemDrawer = () => {
    setIsProblemDrawerOpen(!isProblemDrawerOpen);
  };

  const handleProblemChange = (problemType: number, problemId: string) => {
    if (problemType === 1) {
      router.push(`/basic-problems/${problemId}`);
    } else {
      router.push(`/coding-problems/${problemId}`);
    }
    setIsProblemDrawerOpen(false);
  };

  const handleChange = (problemType: number, problemId: string) => {
    setCurrentProblemId(problemId)
  }

  return (
    <>
      {/* 隐藏公共header */}
      <style jsx global>{`
        nav:not([data-custom="true"]) {
          display: none !important;
        }
      `}</style>

      <nav
        ref={navbarRef}
        data-custom="true"
        className={`bg-white shadow-md w-full z-40 transition-all duration-300 ${
          isScrolled 
            ? 'fixed top-0 left-0 shadow-lg bg-white/95 backdrop-blur-sm' 
            : ''
        }`}
      >
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Image src="/logo.png" alt="信竞星球" width={100} height={40} />
              {/* 题库按钮放在左侧 */}
              <button
                onClick={toggleProblemDrawer}
                className="flex items-center text-indigo-600 hover:text-indigo-800 ml-4"
              >
                <FontAwesomeIcon icon={faBook} className="mr-1" />
                <div className={'w-28 overflow-hidden text-ellipsis text-nowrap text-left'}>{learningContext?.envTitle || '题单'}</div>
              </button>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <Link href="/" className={getLinkClass('/')}>
                首页
              </Link>
              <Link href="/basic-problems" className={getLinkClass('/basic-problems')}>
                基础题库
              </Link>
              <Link href="/coding-problems" className={getLinkClass('/coding-problems')}>
                编程题库
              </Link>
              <Link href="/problem-lists" className={getLinkClass('/problem-lists')}>
                题单
              </Link>
              <Link href="/exams" className={getLinkClass('/exams')}>
                模拟考试
              </Link>
              <Link href="/news-list" className={getLinkClass('/news-list')}>
                编程资讯
              </Link>
              <Link href="/membership" className={getLinkClass('/membership')}>
                会员服务
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              {/* 通知图标 - 只在中等及以上屏幕显示 */}
              <div className="hidden md:block">
                <NotificationDropdown />
              </div>
              {/* 用户信息 - 只在中等及以上屏幕显示 */}
              <div className="hidden md:block">
                <UserInfo />
              </div>
              {/* 菜单按钮 - 只在小屏幕显示 */}
              <button
                className="md:hidden p-2 rounded-full hover:bg-gray-100 transition-colors"
                onClick={toggleDrawer}
                aria-label="打开菜单"
              >
                <FontAwesomeIcon icon={faBars} className="text-gray-600 text-xl" />
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* 抽屉菜单 */}
      <NavDrawer isOpen={isDrawerOpen} onClose={() => setIsDrawerOpen(false)} />

      {/* 题库抽屉 */}
      <ProblemDrawer
        isOpen={isProblemDrawerOpen}
        onClose={() => setIsProblemDrawerOpen(false)}
        onChange={handleChange}
        currentProblemId={currentProblemId}
        currentProblemType={1}
        envTitle={learningContext?.envTitle || '题库'}
        envId={searchParams.get("envId") ? Number(searchParams.get("envId")) : undefined}
        envType={searchParams.get("envType")}
      />

      {/* 占位符，用于固定导航栏 */}
      <div id="navbar-placeholder" className="h-0 transition-all duration-300"></div>

      {/* 主要内容 */}
      {children}
    </>
  );
}
